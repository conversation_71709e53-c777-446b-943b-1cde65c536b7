/**
 * Content utilities for blog posts and rich text handling
 */

// Simple HTML sanitization for blog content
export const sanitizeHTML = (html: string): string => {
  // Allow common HTML tags for blog content
  const allowedTags = [
    'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'code', 'pre', 'span', 'div',
    'table', 'thead', 'tbody', 'tr', 'th', 'td'
  ];

  const allowedAttributes = [
    'href', 'src', 'alt', 'title', 'class', 'id', 'target', 'rel'
  ];

  // Basic sanitization - in production, use a proper library like DOMPurify
  let sanitized = html;

  // Remove script tags and their content
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove style tags and their content
  sanitized = sanitized.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');
  
  // Remove on* event handlers
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  
  // Remove javascript: links
  sanitized = sanitized.replace(/href\s*=\s*["']javascript:[^"']*["']/gi, '');

  return sanitized;
};

// Convert plain text to HTML with basic formatting
export const textToHTML = (text: string): string => {
  return text
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>');
};

// Extract plain text from HTML
export const htmlToText = (html: string): string => {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .trim();
};

// Calculate reading time based on content
export const calculateReadingTime = (content: string): string => {
  const plainText = htmlToText(content);
  const wordsPerMinute = 200; // Average reading speed
  const wordCount = plainText.split(/\s+/).length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  
  return `${minutes} min read`;
};

// Generate excerpt from content
export const generateExcerpt = (content: string, maxLength: number = 160): string => {
  const plainText = htmlToText(content);
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // Find the last complete sentence within the limit
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSentence > maxLength * 0.7) {
    return plainText.substring(0, lastSentence + 1);
  } else if (lastSpace > maxLength * 0.7) {
    return plainText.substring(0, lastSpace) + '...';
  } else {
    return truncated + '...';
  }
};

// Format blog post content for display
export const formatBlogContent = (content: string): string => {
  let formatted = sanitizeHTML(content);
  
  // Add responsive classes to images
  formatted = formatted.replace(
    /<img([^>]*)>/gi, 
    '<img$1 class="w-full h-auto rounded-lg shadow-soft my-6">'
  );
  
  // Add styling to blockquotes
  formatted = formatted.replace(
    /<blockquote([^>]*)>/gi,
    '<blockquote$1 class="border-l-4 border-blue-500 pl-6 py-4 my-6 italic text-muted-foreground bg-muted/30 rounded-r-lg">'
  );
  
  // Add styling to code blocks
  formatted = formatted.replace(
    /<pre([^>]*)>/gi,
    '<pre$1 class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-6">'
  );
  
  // Add styling to inline code
  formatted = formatted.replace(
    /<code([^>]*)>/gi,
    '<code$1 class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">'
  );
  
  // Add styling to tables
  formatted = formatted.replace(
    /<table([^>]*)>/gi,
    '<table$1 class="w-full border-collapse border border-gray-300 dark:border-gray-600 my-6">'
  );
  
  formatted = formatted.replace(
    /<th([^>]*)>/gi,
    '<th$1 class="border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-800 font-semibold text-left">'
  );
  
  formatted = formatted.replace(
    /<td([^>]*)>/gi,
    '<td$1 class="border border-gray-300 dark:border-gray-600 px-4 py-2">'
  );
  
  return formatted;
};

// Validate blog post content
export const validateBlogContent = (content: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!content || content.trim().length === 0) {
    errors.push('Content cannot be empty');
  }
  
  if (content.length < 100) {
    errors.push('Content should be at least 100 characters long');
  }
  
  if (content.length > 50000) {
    errors.push('Content is too long (maximum 50,000 characters)');
  }
  
  // Check for potentially dangerous content
  if (/<script/gi.test(content)) {
    errors.push('Script tags are not allowed');
  }
  
  if (/javascript:/gi.test(content)) {
    errors.push('JavaScript URLs are not allowed');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Generate URL-friendly slug from title
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Check if content contains images
export const hasImages = (content: string): boolean => {
  return /<img[^>]*>/gi.test(content);
};

// Extract all image URLs from content
export const extractImageUrls = (content: string): string[] => {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const urls: string[] = [];
  let match;
  
  while ((match = imgRegex.exec(content)) !== null) {
    urls.push(match[1]);
  }
  
  return urls;
};
