import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import LogoSection from "@/components/LogoSection";
import ElevateSection from "@/components/ElevateSection";
import WhyRistaSection from "@/components/WhyRistaSection";
import ProductSuiteSection from "@/components/ProductSuiteSection";
import RestaurantTypesSection from "@/components/RestaurantTypesSection";
import FeaturesSection from "@/components/FeaturesSection";
import CTASection from "@/components/CTASection";
import Footer from "@/components/Footer";
import withPageLoader from "@/components/withPageLoader";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <HeroSection />
      <LogoSection />
      <ElevateSection />
      <WhyRistaSection />
      <ProductSuiteSection />
      <RestaurantTypesSection />
      <CTASection />
      <Footer />
    </div>
  );
};

export default withPageLoader(Index, {
  loadingText: "Loading Home Page...",
  minLoadingTime: 1000
});
