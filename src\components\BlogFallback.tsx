import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, ExternalLink, Database, Settings } from "lucide-react";

interface BlogFallbackProps {
  error?: string;
  showSetupGuide?: boolean;
}

const BlogFallback = ({ error, showSetupGuide = true }: BlogFallbackProps) => {
  // Fallback blog posts for when Supabase is not configured
  const fallbackPosts = [
    {
      id: "1",
      title: "10 Ways ZYKA POS Can Transform Your Restaurant Operations",
      excerpt: "Discover how modern POS systems are revolutionizing restaurant management and boosting profitability.",
      author: "Eria Team",
      date: "January 15, 2025",
      readTime: "5 min read",
      category: "Restaurant Technology",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop"
    },
    {
      id: "2",
      title: "The Future of Restaurant Technology: Trends to Watch in 2025",
      excerpt: "Explore the latest trends in restaurant technology and how they're shaping the industry.",
      author: "Tech Team",
      date: "January 10, 2025",
      readTime: "7 min read",
      category: "Industry Trends",
      image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=250&fit=crop"
    },
    {
      id: "3",
      title: "How to Choose the Right POS System for Your Restaurant",
      excerpt: "A comprehensive guide to selecting the perfect point-of-sale system for your business needs.",
      author: "Business Team",
      date: "January 5, 2025",
      readTime: "6 min read",
      category: "Buying Guide",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=250&fit=crop"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Configuration Alert */}
      {showSetupGuide && (
        <Alert className="border-orange-200 bg-orange-50">
          <Database className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <div className="space-y-3">
              <div>
                <strong>Supabase Not Configured</strong>
                {error && <span className="block text-sm mt-1">Error: {error}</span>}
              </div>
              <p className="text-sm">
                To display dynamic blog posts from your Supabase database, please configure your environment variables.
              </p>
              <div className="flex flex-wrap gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.open('https://supabase.com', '_blank')}
                  className="text-orange-700 border-orange-300 hover:bg-orange-100"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Get Supabase
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    const element = document.createElement('a');
                    element.href = '/SUPABASE_BLOG_SETUP.md';
                    element.download = 'SUPABASE_BLOG_SETUP.md';
                    element.click();
                  }}
                  className="text-orange-700 border-orange-300 hover:bg-orange-100"
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Setup Guide
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Fallback Content Notice */}
      <Alert className="border-blue-200 bg-blue-50">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Demo Content:</strong> The posts below are sample content. 
          Configure Supabase to display your actual blog posts.
        </AlertDescription>
      </Alert>

      {/* Sample Posts Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {fallbackPosts.map((post) => (
          <Card 
            key={post.id}
            className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 bg-background overflow-hidden opacity-75"
          >
            <div className="relative">
              <img 
                src={post.image} 
                alt={post.title}
                className="w-full h-48 object-cover"
              />
              <Badge className="absolute top-4 left-4 bg-blue-600 text-white text-xs">
                {post.category}
              </Badge>
              <div className="absolute top-4 right-4">
                <Badge variant="secondary" className="text-xs bg-white/90 text-gray-700">
                  Demo
                </Badge>
              </div>
            </div>
            
            <CardContent className="p-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground leading-tight">
                  {post.title}
                </h3>
                
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {post.excerpt}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{post.author}</span>
                    <span>{post.date}</span>
                  </div>
                  <span className="font-medium">{post.readTime}</span>
                </div>

                <Button 
                  variant="link" 
                  className="p-0 font-semibold text-blue-600 hover:text-blue-700 text-sm"
                  disabled
                >
                  Configure Supabase to Enable
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Setup Instructions */}
      {showSetupGuide && (
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Quick Setup Steps
            </h3>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
              <li>Create a Supabase project at <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">supabase.com</a></li>
              <li>Copy your project URL and anon key from Settings → API</li>
              <li>Add them to your <code className="bg-gray-200 px-1 rounded">.env.local</code> file</li>
              <li>Create the blog_posts table using the provided SQL schema</li>
              <li>Add your first blog post and set <code className="bg-gray-200 px-1 rounded">is_published = true</code></li>
            </ol>
            <p className="text-xs text-gray-600 mt-4">
              See <code>SUPABASE_BLOG_SETUP.md</code> for detailed instructions.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BlogFallback;
