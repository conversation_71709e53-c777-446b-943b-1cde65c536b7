# Environment Configuration Example
# Copy this file to .env.local and update the values

# Application Configuration
VITE_APP_NAME=ZYKA POS
VITE_BASE_URL=https://eriasoftware.com

# Google reCAPTCHA Configuration
# Get your keys from: https://www.google.com/recaptcha/admin
# IMPORTANT: Replace with your actual site key to remove test message
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here

# Contact Form Configuration
VITE_FORMSPREE_ENDPOINT=https://formspree.io/f/mjkrvyna

# Cal.com Configuration
VITE_CALCOM_NAMESPACE=eria-software-restaurant-pos-demo
VITE_CALCOM_LINK=eriasoftware/eria-software-restaurant-pos-demo

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# Supabase Configuration (Optional - for blog functionality)
# Get these from your Supabase project dashboard
# If not provided, blog functionality will be disabled gracefully
VITE_SUPABASE_URL=https://buzodtofrksaeuxdvrms.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ1em9kdG9mcmtzYWV1eGR2cm1zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1OTMwMTIsImV4cCI6MjA2OTE2OTAxMn0.9jOYdlxJzw6oCeRj_QgY-5kXViBNgaiprPTsE_3oRMge


# Contact Information
VITE_CONTACT_PHONE=+91-9604069989
VITE_CONTACT_EMAIL=<EMAIL>
VITE_SUPPORT_EMAIL=<EMAIL>
