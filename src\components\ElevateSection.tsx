import { Button } from "@/components/ui/button";

const ElevateSection = () => {
  return (
    <section className="w-full py-16 lg:py-24 bg-background">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto space-y-8">
          <h2 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
            Transform Every Guest Interaction Into Revenue Growth
          </h2>

          <div className="text-3xl lg:text-4xl font-bold text-primary">
            Seamless Operations, Exceptional Experiences.
          </div>

          <p className="text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
            ZYKA POS revolutionizes restaurant management by intelligently automating complex operations
            while preserving the human touch that makes dining memorable. Our advanced system reduces
            order processing time by 40%, minimizes errors, and provides real-time insights that help
            you make profitable decisions. When your operations run smoothly, your team can focus on
            creating extraordinary moments that turn first-time visitors into loyal customers.
          </p>

          <Button
            variant="hero"
            size="lg"
            className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
            onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
          >
            Get a Demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ElevateSection;