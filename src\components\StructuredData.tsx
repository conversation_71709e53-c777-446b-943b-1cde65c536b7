/**
 * Structured Data Component
 * Provides comprehensive schema.org structured data for better SEO
 */

import { useEffect } from 'react';

interface StructuredDataProps {
  type?: 'homepage' | 'product' | 'blog' | 'contact' | 'about';
  data?: any;
}

const StructuredData = ({ type = 'homepage', data }: StructuredDataProps) => {
  useEffect(() => {
    // Remove existing structured data
    const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
    existingScripts.forEach(script => {
      if (script.getAttribute('data-component') === 'structured-data') {
        script.remove();
      }
    });

    // Add new structured data based on type
    const schemas = getSchemasByType(type, data);
    
    schemas.forEach((schema, index) => {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.setAttribute('data-component', 'structured-data');
      script.setAttribute('data-schema-index', index.toString());
      script.textContent = JSON.stringify(schema, null, 2);
      document.head.appendChild(script);
    });

    return () => {
      // Cleanup on unmount
      const scripts = document.querySelectorAll('script[data-component="structured-data"]');
      scripts.forEach(script => script.remove());
    };
  }, [type, data]);

  return null; // This component doesn't render anything visible
};

const getSchemasByType = (type: string, data?: any) => {
  const baseUrl = 'https://eriasoftware.com';
  const currentDate = new Date().toISOString();

  const commonSchemas = [
    // Organization Schema
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "@id": `${baseUrl}/#organization`,
      "name": "Eria Software Solutions and Services Pvt Ltd",
      "alternateName": ["Eria Software", "ZYKA POS"],
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 200,
        "height": 60
      },
      "description": "Leading provider of restaurant management software and POS systems in India, specializing in comprehensive business solutions for the hospitality industry.",
      "foundingDate": "2020",
      "numberOfEmployees": {
        "@type": "QuantitativeValue",
        "minValue": 10,
        "maxValue": 50
      },
      "contactPoint": [
        {
          "@type": "ContactPoint",
          "telephone": "+91-**********",
          "contactType": "customer service",
          "email": "<EMAIL>",
          "availableLanguage": ["English", "Hindi"],
          "areaServed": "IN",
          "hoursAvailable": {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
            "opens": "09:00",
            "closes": "18:00"
          }
        },
        {
          "@type": "ContactPoint",
          "telephone": "+91-**********",
          "contactType": "sales",
          "email": "<EMAIL>",
          "availableLanguage": ["English", "Hindi"],
          "areaServed": "IN"
        },
        {
          "@type": "ContactPoint",
          "contactType": "technical support",
          "email": "<EMAIL>",
          "availableLanguage": ["English", "Hindi"],
          "areaServed": "IN"
        }
      ],
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN",
        "addressRegion": "India"
      },
      "sameAs": [
        "https://www.facebook.com/eriasoftware",
        "https://www.linkedin.com/company/eria-software",
        "https://twitter.com/eriasoftware",
        "https://www.instagram.com/eriasoftware"
      ],
      "knowsAbout": [
        "Restaurant Management Software",
        "Point of Sale Systems",
        "Inventory Management",
        "Staff Management",
        "Restaurant Technology",
        "Business Automation",
        "WhatsApp Integration",
        "Cloud-based Solutions"
      ],
      "makesOffer": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "SoftwareApplication",
            "name": "ZYKA POS",
            "applicationCategory": "BusinessApplication"
          },
          "availability": "https://schema.org/InStock",
          "priceCurrency": "INR"
        }
      ],
      "award": [
        "Best Restaurant POS Software 2024",
        "Innovation in Restaurant Technology"
      ]
    },

    // LocalBusiness Schema
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "@id": `${baseUrl}/#localbusiness`,
      "name": "Eria Software Solutions and Services Pvt Ltd",
      "image": `${baseUrl}/og-image.png`,
      "description": "Professional restaurant management software and POS system provider serving businesses across India with cutting-edge technology solutions.",
      "url": baseUrl,
      "telephone": "+91-**********",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN",
        "addressRegion": "India"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": 20.5937,
        "longitude": 78.9629
      },
      "openingHoursSpecification": [
        {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
          "opens": "09:00",
          "closes": "18:00"
        }
      ],
      "priceRange": "₹₹",
      "currenciesAccepted": "INR",
      "paymentAccepted": ["Cash", "Credit Card", "Debit Card", "Bank Transfer", "UPI"],
      "areaServed": {
        "@type": "Country",
        "name": "India"
      },
      "serviceArea": {
        "@type": "Country",
        "name": "India"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "150",
        "bestRating": "5",
        "worstRating": "1"
      },
      "review": [
        {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5",
            "bestRating": "5"
          },
          "author": {
            "@type": "Person",
            "name": "Restaurant Owner"
          },
          "reviewBody": "ZYKA POS has transformed our restaurant operations. Highly recommended!"
        }
      ]
    },

    // WebSite Schema
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "@id": `${baseUrl}/#website`,
      "name": "Eria Software - Restaurant Management Solutions",
      "alternateName": "ZYKA POS",
      "url": baseUrl,
      "description": "Complete restaurant management solutions and POS systems for modern businesses",
      "publisher": {
        "@type": "Organization",
        "@id": `${baseUrl}/#organization`
      },
      "potentialAction": [
        {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": `${baseUrl}/search?q={search_term_string}`
          },
          "query-input": "required name=search_term_string"
        }
      ],
      "mainEntity": {
        "@type": "SoftwareApplication",
        "@id": `${baseUrl}/#software`
      },
      "inLanguage": "en-US",
      "copyrightYear": "2025",
      "copyrightHolder": {
        "@type": "Organization",
        "@id": `${baseUrl}/#organization`
      }
    }
  ];

  // Add type-specific schemas
  switch (type) {
    case 'homepage':
      return [
        ...commonSchemas,
        // Product Schema for ZYKA POS
        {
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "@id": `${baseUrl}/#software`,
          "name": "ZYKA POS",
          "alternateName": "ZYKA Point of Sale System",
          "applicationCategory": "BusinessApplication",
          "applicationSubCategory": "Restaurant Management Software",
          "operatingSystem": ["Web", "iOS", "Android", "Windows"],
          "description": "Complete restaurant management system with advanced POS, inventory control, staff management, and WhatsApp integration features",
          "url": baseUrl,
          "downloadUrl": `${baseUrl}/demo`,
          "installUrl": `${baseUrl}/demo`,
          "softwareVersion": "2.0",
          "releaseNotes": "Enhanced performance, new features, and improved user interface",
          "datePublished": "2020-01-01",
          "dateModified": currentDate,
          "author": {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`
          },
          "publisher": {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`
          },
          "offers": {
            "@type": "Offer",
            "category": "Software License",
            "availability": "https://schema.org/InStock",
            "priceCurrency": "INR",
            "seller": {
              "@type": "Organization",
              "@id": `${baseUrl}/#organization`
            },
            "validFrom": currentDate
          },
          "featureList": [
            "Advanced Point of Sale System",
            "Real-time Inventory Management",
            "Staff Management & Scheduling",
            "WhatsApp Business Integration",
            "Multi-location Support",
            "Cloud-based Architecture",
            "Real-time Analytics & Reporting",
            "Customer Relationship Management",
            "Payment Gateway Integration",
            "Tax Management & Compliance"
          ],
          "screenshot": `${baseUrl}/og-image.png`,
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "150",
            "bestRating": "5",
            "worstRating": "1"
          },
          "review": [
            {
              "@type": "Review",
              "reviewRating": {
                "@type": "Rating",
                "ratingValue": "5",
                "bestRating": "5"
              },
              "author": {
                "@type": "Person",
                "name": "Rajesh Kumar"
              },
              "reviewBody": "Excellent POS system with great customer support. Has improved our restaurant efficiency significantly."
            }
          ]
        }
      ];

    case 'contact':
      return [
        ...commonSchemas,
        // ContactPage Schema
        {
          "@context": "https://schema.org",
          "@type": "ContactPage",
          "name": "Contact Eria Software",
          "description": "Get in touch with Eria Software for restaurant management solutions and support",
          "url": `${baseUrl}/contact`,
          "mainEntity": {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`
          }
        }
      ];

    case 'about':
      return [
        ...commonSchemas,
        // AboutPage Schema
        {
          "@context": "https://schema.org",
          "@type": "AboutPage",
          "name": "About Eria Software",
          "description": "Learn about Eria Software's mission to revolutionize restaurant management through innovative technology solutions",
          "url": `${baseUrl}/about-us`,
          "mainEntity": {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`
          }
        }
      ];

    default:
      return commonSchemas;
  }
};

export default StructuredData;
