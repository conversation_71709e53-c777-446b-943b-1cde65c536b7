import { TrendingUp } from "lucide-react";

const LogoSection = () => {
  // Mock customer logos with different colors and sizes
  const customers = [
    { name: "<PERSON><PERSON>s", color: "bg-orange-500" },
    { name: "<PERSON><PERSON><PERSON>", color: "bg-blue-400" },
    { name: "Meghana Foods", color: "bg-green-600" },
    { name: "Nic Ice Cream", color: "bg-purple-500" },
    { name: "<PERSON><PERSON><PERSON>", color: "bg-red-500" },
    { name: "Smoor", color: "bg-gray-800" },
    { name: "Ribbons", color: "bg-pink-500" },
    { name: "Absolute BBQ", color: "bg-yellow-500" },
    { name: "Captain Sam's", color: "bg-indigo-600" },
    { name: "Brooklyn", color: "bg-teal-500" },
    { name: "FabCafe", color: "bg-purple-600" },
    { name: "<PERSON><PERSON><PERSON>", color: "bg-orange-600" },
  ];

  return (
    <section className="w-full py-16 bg-background">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-foreground">
            Trusted by 100+ restaurants{" "}
            <span className="inline-flex items-center gap-2">
              achieving remarkable growth
              <TrendingUp className="w-6 h-6 text-primary" />
            </span>{" "}
            with Zyka POS technology.
          </h2>
        </div>

        {/* Customer Logos Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6 items-center justify-items-center">
          {customers.map((customer, index) => (
            <div
              key={index}
              className={`
                ${customer.color} 
                rounded-lg p-4 w-20 h-16 flex items-center justify-center
                transition-transform duration-200 hover:scale-105 cursor-pointer
                shadow-sm hover:shadow-md
              `}
            >
              <span className="text-white font-bold text-xs text-center leading-tight">
                {customer.name}
              </span>
            </div>
          ))}
        </div>

        {/* Additional Stats */}
        <div className="mt-16 text-center">
          <div className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div>
              <div className="text-3xl font-bold text-primary">100+</div>
              <div className="text-sm text-muted-foreground">Active Restaurants</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">100K+</div>
              <div className="text-sm text-muted-foreground">Orders Processed</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">10+</div>
              <div className="text-sm text-muted-foreground">Cities Covered</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">20+</div>
              <div className="text-sm text-muted-foreground">Team Members</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LogoSection;