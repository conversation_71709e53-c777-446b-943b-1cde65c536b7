import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  ShoppingCart, 
  Zap, 
  BarChart3, 
  Users, 
  Clock, 
  Shield,
  Smartphone,
  CreditCard,
  Printer,
  Wifi
} from "lucide-react";

const PointOfSale = () => {
  const features = [
    {
      icon: ShoppingCart,
      title: "Lightning-Fast Billing",
      description: "Process orders 3x faster with our intuitive interface and smart shortcuts."
    },
    {
      icon: BarChart3,
      title: "Real-Time Analytics",
      description: "Get instant insights into sales performance and customer behavior."
    },
    {
      icon: Users,
      title: "Multi-User Support",
      description: "Multiple staff members can use the system simultaneously without conflicts."
    },
    {
      icon: Clock,
      title: "Quick Setup",
      description: "Get your POS system running in under 30 minutes with easy configuration."
    },
    {
      icon: Shield,
      title: "Secure Payments",
      description: "PCI DSS compliant payment processing with end-to-end encryption."
    },
    {
      icon: Smartphone,
      title: "Mobile Ready",
      description: "Access your POS system from any device, anywhere, anytime."
    }
  ];

  const benefits = [
    "Reduce transaction time by 60%",
    "Eliminate billing errors completely",
    "Support for 50+ payment methods",
    "Offline mode for uninterrupted service",
    "Customizable receipt templates",
    "Integration with kitchen displays"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                  Advanced Point of Sale System
                </Badge>
                
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  Revolutionary POS System for Modern Restaurants
                </h1>
                
                <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                  Experience lightning-fast billing, seamless order management, and powerful 
                  analytics with ZYKA POS's advanced point-of-sale system designed specifically
                  for restaurants.
                </p>
              </div>

              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Start Free Trial
              </Button>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">30-day free trial</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">No setup fees</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop" 
                alt="POS System Interface" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Powerful POS Features That Drive Results
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to streamline your restaurant operations and boost profitability.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                Why Choose ZYKA POS System?
              </h2>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-foreground">{benefit}</span>
                  </div>
                ))}
              </div>

              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Get Demo
              </Button>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop" 
                alt="Restaurant POS Benefits" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Transform Your Restaurant Operations?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Join thousands of restaurants using ZYKA POS to streamline operations and increase revenue.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Start Free Trial
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Schedule Demo
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Free setup & training • ✓ 30-day money-back guarantee • ✓ 24/7 support included
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(PointOfSale, {
  loadingText: "Loading Point of Sale...",
  minLoadingTime: 1000
});
