import { Card, CardContent } from "@/components/ui/card";
import { 
  ShoppingCart, 
  Package, 
  Smartphone, 
  TrendingUp, 
  Users, 
  MessageCircle 
} from "lucide-react";

const FeaturesSection = () => {
  const features = [
    {
      icon: ShoppingCart,
      title: "Advanced Point of Sale System",
      description: "Experience lightning-fast order processing with our cloud-based POS that handles complex orders, split billing, table management, and multi-payment options. Reduce transaction time by 60% while ensuring 99.9% uptime reliability."
    },
    {
      icon: Package,
      title: "Smart Inventory Control",
      description: "Eliminate stockouts and reduce waste with AI-powered inventory management featuring predictive analytics, automated reordering, recipe costing, and supplier performance tracking. Cut inventory costs by up to 25%."
    },
    {
      icon: Smartphone,
      title: "Zero-Commission Online Ordering",
      description: "Boost revenue with our integrated online ordering platform featuring QR code menus, mobile apps, and website integration. Increase average order value by 27% while eliminating third-party commission fees."
    },
    {
      icon: TrendingUp,
      title: "Business Intelligence Dashboard",
      description: "Make data-driven decisions with comprehensive analytics covering sales trends, customer behavior, staff performance, and profitability metrics. Access real-time insights and automated reports for strategic planning."
    },
    {
      icon: Users,
      title: "Complete Staff Management",
      description: "Streamline workforce operations with advanced scheduling, time tracking, performance monitoring, and role-based access controls. Reduce labor costs while improving staff productivity and accountability."
    },
    {
      icon: MessageCircle,
      title: "WhatsApp Business Integration",
      description: "Engage customers directly through WhatsApp with automated order confirmations, promotional campaigns, feedback collection, and customer support. Increase customer retention by 40% through personalized communication."
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
            Complete Restaurant Management Ecosystem
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover how ZYKA POS's integrated suite of powerful features transforms every aspect
            of your restaurant operations, from front-of-house service to back-office management,
            delivering measurable results and sustainable growth.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
            >
              <CardContent className="p-8">
                <div className="space-y-4">
                  <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-primary-foreground" />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-foreground">
                    {feature.title}
                  </h3>
                  
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;