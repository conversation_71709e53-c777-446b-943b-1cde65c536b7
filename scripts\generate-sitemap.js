#!/usr/bin/env node

/**
 * Generate Sitemap Script
 * This script generates a dynamic sitemap.xml file including blog posts
 * Run this script during the build process to update the sitemap
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Static pages configuration
const staticPages = [
  {
    loc: 'https://eriasoftware.com/',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'daily',
    priority: 1.0,
    images: [{
      loc: 'https://eriasoftware.com/og-image.png',
      title: 'ZYKA POS - Complete Restaurant Management System',
      caption: 'Transform your restaurant operations with ZYKA POS'
    }]
  },
  {
    loc: 'https://eriasoftware.com/point-of-sale',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.95
  },
  {
    loc: 'https://eriasoftware.com/inventory',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.95
  },
  {
    loc: 'https://eriasoftware.com/demo',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.95
  },
  {
    loc: 'https://eriasoftware.com/website-development',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/app-development',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/freshservice-crm',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/contact',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/support',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.80
  },
  {
    loc: 'https://eriasoftware.com/about-us',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.75
  },
  {
    loc: 'https://eriasoftware.com/blog',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'daily',
    priority: 0.80
  },
  // Legal pages
  {
    loc: 'https://eriasoftware.com/privacy-policy',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.40
  },
  {
    loc: 'https://eriasoftware.com/terms-of-service',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.40
  },
  {
    loc: 'https://eriasoftware.com/cookie-policy',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.35
  },
  {
    loc: 'https://eriasoftware.com/accessibility-statement',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'yearly',
    priority: 0.35
  },
  {
    loc: 'https://eriasoftware.com/security-policy',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.40
  }
];

// Sample blog posts (these would normally come from your database)
const sampleBlogPosts = [
  {
    slug: '10-ways-zyka-pos-transform-restaurant-operations',
    title: '10 Ways ZYKA POS Can Transform Your Restaurant Operations',
    publishedDate: new Date().toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop'
  },
  {
    slug: 'restaurant-inventory-management-best-practices',
    title: 'Restaurant Inventory Management: Best Practices for 2025',
    publishedDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=250&fit=crop'
  },
  {
    slug: 'choosing-right-pos-system-restaurant',
    title: 'How to Choose the Right POS System for Your Restaurant',
    publishedDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=250&fit=crop'
  },
  {
    slug: 'restaurant-technology-trends-2025',
    title: 'Restaurant Technology Trends to Watch in 2025',
    publishedDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=250&fit=crop'
  },
  {
    slug: 'whatsapp-integration-restaurant-business',
    title: 'WhatsApp Integration: Revolutionizing Restaurant Customer Service',
    publishedDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=250&fit=crop'
  }
];

// Convert blog post to sitemap URL
const blogPostToSitemapUrl = (post) => {
  const publishedDate = new Date(post.publishedDate);
  const isRecent = (Date.now() - publishedDate.getTime()) < (30 * 24 * 60 * 60 * 1000); // 30 days

  return {
    loc: `https://eriasoftware.com/blog/${post.slug}`,
    lastmod: publishedDate.toISOString().split('T')[0],
    changefreq: isRecent ? 'weekly' : 'monthly',
    priority: isRecent ? 0.75 : 0.70,
    images: post.imageUrl ? [{
      loc: post.imageUrl,
      title: post.title,
      caption: `Read more about ${post.title} on ERIA Software Blog`
    }] : undefined,
    news: isRecent ? {
      publication: {
        name: 'ERIA Software Blog',
        language: 'en'
      },
      publication_date: publishedDate.toISOString().split('T')[0],
      title: post.title
    } : undefined
  };
};

// Escape XML special characters
const escapeXML = (str) => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
};

// Generate XML sitemap
const generateSitemapXML = () => {
  const blogUrls = sampleBlogPosts.map(blogPostToSitemapUrl);
  const allUrls = [...staticPages, ...blogUrls];
  
  const urlsXML = allUrls.map(url => {
    let urlXML = `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>`;

    // Add images if present
    if (url.images && url.images.length > 0) {
      url.images.forEach(image => {
        urlXML += `
    <image:image>
      <image:loc>${image.loc}</image:loc>`;
        if (image.title) urlXML += `
      <image:title>${escapeXML(image.title)}</image:title>`;
        if (image.caption) urlXML += `
      <image:caption>${escapeXML(image.caption)}</image:caption>`;
        urlXML += `
    </image:image>`;
      });
    }

    // Add news if present
    if (url.news) {
      urlXML += `
    <news:news>
      <news:publication>
        <news:name>${escapeXML(url.news.publication.name)}</news:name>
        <news:language>${url.news.publication.language}</news:language>
      </news:publication>
      <news:publication_date>${url.news.publication_date}</news:publication_date>
      <news:title>${escapeXML(url.news.title)}</news:title>
    </news:news>`;
    }

    urlXML += `
  </url>`;
    return urlXML;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
${urlsXML}
</urlset>`;
};

// Main function
const main = () => {
  try {
    console.log('🚀 Generating sitemap...');
    
    const sitemapXML = generateSitemapXML();
    const publicDir = path.join(__dirname, '..', 'public');
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    
    // Ensure public directory exists
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    
    // Write sitemap to file
    fs.writeFileSync(sitemapPath, sitemapXML, 'utf8');
    
    console.log('✅ Sitemap generated successfully!');
    console.log(`📍 Location: ${sitemapPath}`);
    console.log(`📊 Total URLs: ${staticPages.length + sampleBlogPosts.length}`);
    
  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  }
};

// Run the script
main();
