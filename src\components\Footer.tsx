import { Facebook, Instagram, Linkedin, Twitter } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="w-full bg-black border-t border-gray-800 py-12">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-white">Eria Software</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              Leading provider of ZYKA POS software and comprehensive IT solutions for restaurants,
              cafes, and businesses across India. Trusted by 50+ happy clients.
            </p>
            <div className="space-y-2 mt-4">
              <p className="text-gray-300 text-sm">
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </p>
              <p className="text-gray-300 text-sm">
                <a href="tel:+************" className="hover:text-white transition-colors">
                  +91 **********
                </a>
              </p>
              <p className="text-gray-300 text-sm">Mumbai, Maharashtra</p>
            </div>
          </div>

          {/* Product Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">Products</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="https://zykapos.eriasoftware.com" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white transition-colors">Point of Sale</a></li>
              <li><Link to="/inventory" className="text-gray-300 hover:text-white transition-colors">Inventory Management</Link></li>
              <li><Link to="/" className="text-gray-300 hover:text-white transition-colors">ZYKA POS Software</Link></li>
            </ul>
          </div>

          {/* Services Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">Services</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/website-development" className="text-gray-300 hover:text-white transition-colors">Website Development</Link></li>
              <li><Link to="/app-development" className="text-gray-300 hover:text-white transition-colors">App Development</Link></li>
              <li><Link to="/freshservice-crm" className="text-gray-300 hover:text-white transition-colors">FreshService CRM</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-white transition-colors">Contact Us</Link></li>
              <li><Link to="/support" className="text-gray-300 hover:text-white transition-colors">Support</Link></li>
            </ul>
          </div>

          {/* Company Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">Company</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/about-us" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
              <li><Link to="/blog" className="text-gray-300 hover:text-white transition-colors">Blog</Link></li>
              <li><Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link to="/terms-of-service" className="text-gray-300 hover:text-white transition-colors">Terms of Service</Link></li>
              <li><Link to="/accessibility-statement" className="text-gray-300 hover:text-white transition-colors">Accessibility</Link></li>
              <li><Link to="/security-policy" className="text-gray-300 hover:text-white transition-colors">Security</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-300">
            © 2024 Eria Software Solutions and Services Pvt Ltd. All rights reserved.
          </p>
          <div className="flex flex-col md:flex-row items-center gap-4 mt-4 md:mt-0">
            <div className="flex flex-wrap space-x-4 text-sm">
              <Link to="/terms-of-service" className="text-gray-300 hover:text-white transition-colors">
                Terms
              </Link>
              <Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">
                Privacy
              </Link>
              <Link to="/cookie-policy" className="text-gray-300 hover:text-white transition-colors">
                Cookies
              </Link>
              <Link to="/accessibility-statement" className="text-gray-300 hover:text-white transition-colors">
                Accessibility
              </Link>
              <Link to="/security-policy" className="text-gray-300 hover:text-white transition-colors">
                Security
              </Link>
            </div>

            {/* Social Media Icons */}
            <div className="flex space-x-4">
              <a
                href="https://linkedin.com/company/eria-software"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="https://instagram.com/eriasoftware"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a
                href="https://facebook.com/eriasoftware"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="https://x.com/eriasoftware"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;