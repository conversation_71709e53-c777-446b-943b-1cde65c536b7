import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bad<PERSON> } from "@/components/ui/badge";
import { Star } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import heroImage from "@/assets/restaurant-hero.jpg";
import posInterface from "@/assets/pos-interface.jpg";

const HeroSection = () => {
  return (
    <section className="w-full bg-gradient-hero py-16 lg:py-24">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                Complete Restaurant Management Platform
              </Badge>

              <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                Revolutionize Your Restaurant Operations with ZYKA POS
              </h1>

              <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                Transform your restaurant with intelligent POS technology that streamlines operations
                and maximizes profitability. Experience faster service, smarter inventory management,
                and exceptional customer experiences.
              </p>
            </div>

            <Button
              variant="hero"
              size="lg"
              className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
            >
              Get a Demo
            </Button>

            {/* Trust Indicators */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-primary fill-primary" />
                <span className="text-sm font-medium text-foreground">Trusted by 100+ Restaurants</span>
                <span className="text-sm text-muted-foreground">Across India</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-primary fill-primary" />
                <span className="text-sm font-medium text-foreground">100K+ Orders Processed</span>
                <span className="text-sm text-muted-foreground">Across 10+ Cities</span>
              </div>
            </div>
          </div>

          {/* Right Content - Images */}
          <div className="relative">
            <div className="relative">
              <img 
                src={heroImage} 
                alt="Restaurant professional" 
                className="w-full h-auto rounded-lg shadow-soft"
              />
              
              {/* POS Interface Overlay */}
              <div className="absolute -bottom-6 -left-6 lg:-left-12 w-3/4 max-w-sm">
                <img 
                  src={posInterface} 
                  alt="Restaurant POS Interface" 
                  className="w-full h-auto rounded-lg shadow-soft border-4 border-background"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;