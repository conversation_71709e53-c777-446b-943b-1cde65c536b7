import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const ProductSuiteSection = () => {
  const products = [
    {
      id: "website-development",
      category: "WEBSITE DEVELOPMENT",
      title: "Professional Restaurant Websites",
      description: "Create stunning, responsive websites that showcase your restaurant's brand and drive online orders. Our custom web development solutions include online menu displays, reservation systems, customer reviews integration, and mobile-optimized designs that convert visitors into customers.",
      features: [
        "Mobile-Responsive Design",
        "Online Menu Integration",
        "Reservation System"
      ],
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop"
    },
    {
      id: "app-development",
      category: "APP DEVELOPMENT",
      title: "Custom Mobile Applications",
      description: "Engage customers with native mobile apps that offer seamless ordering, loyalty programs, and push notifications. Our app development services create user-friendly interfaces that increase customer retention and boost repeat orders through personalized experiences.",
      features: [
        "Native iOS & Android Apps",
        "Push Notifications",
        "Loyalty Program Integration"
      ],
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop"
    },
    {
      id: "freshservice-crm",
      category: "FRESHSERVICE CRM",
      title: "Complete Customer Relationship Management",
      description: "Streamline your customer service and support operations with our comprehensive CRM solution. Manage customer inquiries, track service requests, automate follow-ups, and maintain detailed customer profiles to deliver exceptional service experiences that build loyalty.",
      features: [
        "Customer Service Automation",
        "Service Request Tracking",
        "Customer Profile Management"
      ],
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop"
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-background">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-sm font-semibold text-primary mb-4 uppercase tracking-wide">
            Our Product Suite
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4">
            Fuelling success for every business,<br />
            all types, every size.
          </h2>
          
          {/* Product Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            {products.map((product) => (
              <Badge key={product.id} variant="outline" className="px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors">
                {product.category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Products */}
        <div className="space-y-24">
          {products.map((product, index) => (
            <div 
              key={product.id} 
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={`space-y-6 ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <Badge variant="secondary" className="text-sm font-medium">
                  {product.category}
                </Badge>
                
                <h3 className="text-3xl lg:text-4xl font-bold text-foreground">
                  {product.title}
                </h3>
                
                <p className="text-lg text-muted-foreground leading-relaxed">
                  {product.description}
                </p>

                <ul className="space-y-2">
                  {product.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-2 text-foreground">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button variant="link" className="p-0 font-semibold text-black hover:text-gray-700">
                  Learn More
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>

              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                <Card className="overflow-hidden shadow-soft hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <img 
                      src={product.image} 
                      alt={product.title}
                      className="w-full h-80 object-cover"
                    />
                  </CardContent>
                </Card>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductSuiteSection;