import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Check if Supabase is configured
const isSupabaseConfigured = !!(supabaseUrl && supabaseAnonKey);

if (!isSupabaseConfigured) {
  console.warn('Supabase environment variables not found. Blog functionality will be disabled.');
}

// Create Supabase client only if configured
export const supabase = isSupabaseConfigured
  ? createClient(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        persistSession: false, // Since this is a public blog, we don't need session persistence
      },
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'X-Client-Info': 'eria-software-website',
        },
      },
    })
  : null;

// Database types for better TypeScript support
export type Database = {
  public: {
    Tables: {
      posts: {
        Row: {
          id: string;
          title: string;
          slug: string;
          content: string;
          excerpt?: string;
          featured_image?: string;
          status: 'draft' | 'published' | 'archived';
          author_id: string;
          category_id?: string;
          published_at?: string;
          created_at: string;
          updated_at: string;
          version: number;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          content: string;
          excerpt?: string;
          featured_image?: string;
          status?: 'draft' | 'published' | 'archived';
          author_id: string;
          category_id?: string;
          published_at?: string;
          created_at?: string;
          updated_at?: string;
          version?: number;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          content?: string;
          excerpt?: string;
          featured_image?: string;
          status?: 'draft' | 'published' | 'archived';
          author_id?: string;
          category_id?: string;
          published_at?: string;
          created_at?: string;
          updated_at?: string;
          version?: number;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description?: string;
          color?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string;
          color?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string;
          color?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          email: string;
          role: 'admin' | 'editor' | 'viewer';
          name?: string;
          avatar_url?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          role?: 'admin' | 'editor' | 'viewer';
          name?: string;
          avatar_url?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          role?: 'admin' | 'editor' | 'viewer';
          name?: string;
          avatar_url?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      tags: {
        Row: {
          id: string;
          name: string;
          slug: string;
          color?: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          color?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          color?: string;
          created_at?: string;
        };
      };
    };
  };
};

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  
  if (error?.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred while fetching data.';
};

// Connection test function
export const testSupabaseConnection = async (): Promise<boolean> => {
  if (!supabase) {
    console.warn('Supabase not configured');
    return false;
  }

  try {
    const { data, error } = await supabase
      .from('posts')
      .select('id')
      .limit(1);

    if (error) {
      console.error('Supabase connection test failed:', error);
      return false;
    }

    console.log('Supabase connection successful');
    return true;
  } catch (error) {
    console.error('Supabase connection test error:', error);
    return false;
  }
};

// Debug function to check CSP and connection
export const debugSupabaseConnection = async (): Promise<void> => {
  console.log('=== Supabase Debug Information ===');
  console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
  console.log('Supabase Key (first 20 chars):', import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...');
  console.log('Supabase configured:', isSupabaseConfigured);

  // Check CSP
  const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  if (cspMeta) {
    console.log('CSP found:', cspMeta.getAttribute('content'));
  } else {
    console.log('No CSP meta tag found');
  }

  // Test connection
  console.log('Testing connection...');
  const isConnected = await testSupabaseConnection();
  console.log('Connection result:', isConnected);
  console.log('=== End Debug Information ===');
};
