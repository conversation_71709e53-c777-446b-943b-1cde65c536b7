import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Phone } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const CTASection = () => {
  return (
    <section className="w-full py-16 lg:py-24 bg-black">
      <div className="container mx-auto px-6 lg:px-8 text-center">
        <div className="max-w-3xl mx-auto space-y-8">
          <h2 className="text-3xl lg:text-5xl font-bold text-white">
            Start Your Restaurant Revolution Today
          </h2>

          <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
            Join 100+ successful restaurants that have increased their revenue by 30% and reduced
            operational costs by 25% with ZYKA POS. Experience the difference that intelligent restaurant
            management technology can make for your business growth and customer satisfaction.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              variant="hero"
              size="lg"
              className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
            >
              Get a Free Demo
              <ArrowRight className="w-5 h-5" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
            >
              <Phone className="w-5 h-5" />
              Call Us Now
            </Button>
          </div>

          <div className="pt-8 text-sm text-white/80">
            ✓ Free setup & training • ✓ 30-day money-back guarantee • ✓ 24/7 support included
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;