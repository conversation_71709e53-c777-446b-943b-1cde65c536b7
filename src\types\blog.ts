// Blog post interface for the frontend
export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  authorAvatar?: string;
  publishedDate: string;
  readTime: string;
  category: string;
  imageUrl: string;
  slug: string;
  tags?: string[];
  isPublished: boolean;
  metaTitle?: string;
  metaDescription?: string;
  createdAt: string;
  updatedAt: string;
}

// Blog category interface
export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: string;
}

// Blog post preview interface (for listing pages)
export interface BlogPostPreview {
  id: string;
  title: string;
  excerpt: string;
  author: string;
  authorAvatar?: string;
  publishedDate: string;
  readTime: string;
  category: string;
  imageUrl: string;
  slug: string;
  tags?: string[];
}

// Blog post filters
export interface BlogFilters {
  category?: string;
  tag?: string;
  author?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

// Blog post sort options
export type BlogSortOption = 'newest' | 'oldest' | 'popular' | 'alphabetical';

// API response interfaces
export interface BlogPostsResponse {
  posts: BlogPost[];
  total: number;
  hasMore: boolean;
}

export interface BlogCategoriesResponse {
  categories: BlogCategory[];
}

// Error interface for blog operations
export interface BlogError {
  message: string;
  code?: string;
  details?: any;
}

// Blog post form data (for admin/CMS use)
export interface BlogPostFormData {
  title: string;
  excerpt: string;
  content: string;
  author: string;
  authorAvatar?: string;
  category: string;
  imageUrl: string;
  slug: string;
  tags?: string[];
  isPublished: boolean;
  metaTitle?: string;
  metaDescription?: string;
}

// Utility function to convert database row to BlogPost
export const mapDatabaseRowToBlogPost = (row: any): BlogPost => {
  return {
    id: row.id,
    title: row.title,
    excerpt: row.excerpt,
    content: row.content,
    author: row.author,
    authorAvatar: row.author_avatar,
    publishedDate: row.published_date,
    readTime: row.read_time,
    category: row.category,
    imageUrl: row.image_url,
    slug: row.slug,
    tags: row.tags,
    isPublished: row.is_published,
    metaTitle: row.meta_title,
    metaDescription: row.meta_description,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
};

// Utility function to convert BlogPost to database row
export const mapBlogPostToDatabaseRow = (post: BlogPostFormData): any => {
  return {
    title: post.title,
    excerpt: post.excerpt,
    content: post.content,
    author: post.author,
    author_avatar: post.authorAvatar,
    category: post.category,
    image_url: post.imageUrl,
    slug: post.slug,
    tags: post.tags,
    is_published: post.isPublished,
    meta_title: post.metaTitle,
    meta_description: post.metaDescription,
  };
};

// Utility function to generate slug from title
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
};

// Utility function to calculate read time
export const calculateReadTime = (content: string): string => {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return `${minutes} min read`;
};

// Utility function to format date for display
export const formatBlogDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};
