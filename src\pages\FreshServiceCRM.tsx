import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  Users, 
  MessageCircle, 
  BarChart3, 
  Clock, 
  Shield, 
  Zap,
  CheckCircle,
  Phone,
  Mail,
  Settings
} from "lucide-react";

const FreshServiceCRM = () => {
  const features = [
    {
      icon: Users,
      title: "Customer Management",
      description: "Centralized customer database with complete interaction history and preferences."
    },
    {
      icon: MessageCircle,
      title: "Multi-Channel Support",
      description: "Handle customer inquiries across email, phone, chat, and social media from one platform."
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Detailed reports and insights to track customer satisfaction and team performance."
    },
    {
      icon: Clock,
      title: "Automated Workflows",
      description: "Streamline repetitive tasks with intelligent automation and ticket routing."
    },
    {
      icon: Shield,
      title: "Data Security",
      description: "Enterprise-grade security with role-based access controls and data encryption."
    },
    {
      icon: Zap,
      title: "Quick Integration",
      description: "Seamless integration with your existing business tools and software systems."
    }
  ];

  const benefits = [
    "Improve customer satisfaction by 40%",
    "Reduce response time by 60%",
    "Increase team productivity by 35%",
    "Centralize all customer communications",
    "Automate routine support tasks",
    "Generate detailed performance reports"
  ];

  const serviceTypes = [
    {
      title: "Help Desk Management",
      description: "Comprehensive ticket management system for efficient customer support",
      features: ["Ticket routing", "Priority management", "SLA tracking", "Knowledge base"]
    },
    {
      title: "Customer Service Portal",
      description: "Self-service portal for customers to find answers and submit requests",
      features: ["FAQ system", "Ticket submission", "Status tracking", "Resource library"]
    },
    {
      title: "Team Collaboration",
      description: "Tools for seamless collaboration between support team members",
      features: ["Internal notes", "Team assignments", "Escalation rules", "Performance metrics"]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                  FreshService CRM Solutions
                </Badge>
                
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  Transform Customer Service with FreshService CRM
                </h1>
                
                <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                  Deliver exceptional customer experiences with our comprehensive CRM solution. 
                  Streamline support processes, improve response times, and build lasting customer relationships.
                </p>
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Get Started Today
              </Button>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">Free setup and training</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">24/7 customer support</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1553028826-f4804a6dfd3f?w=600&h=400&fit=crop" 
                alt="FreshService CRM Dashboard" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Powerful CRM Features for Modern Businesses
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to deliver exceptional customer service and build stronger relationships.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Service Types */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Comprehensive CRM Solutions
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose from our range of CRM services tailored to your business needs.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {serviceTypes.map((service, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        {service.title}
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        {service.description}
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <span className="text-sm text-foreground">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                Why Choose FreshService CRM?
              </h2>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-foreground">{benefit}</span>
                  </div>
                ))}
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Request Demo
              </Button>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=600&h=400&fit=crop" 
                alt="CRM Benefits" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Proven Results with FreshService CRM
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">40%</div>
              <div className="text-lg font-medium text-foreground mb-2">Higher Satisfaction</div>
              <div className="text-muted-foreground">Improved customer satisfaction rates</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">60%</div>
              <div className="text-lg font-medium text-foreground mb-2">Faster Response</div>
              <div className="text-muted-foreground">Reduced average response time</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">35%</div>
              <div className="text-lg font-medium text-foreground mb-2">Team Productivity</div>
              <div className="text-muted-foreground">Increase in team efficiency</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Transform Your Customer Service?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Join businesses that have improved their customer satisfaction with FreshService CRM.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              >
                Start Free Trial
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                <Phone className="w-5 h-5 mr-2" />
                Schedule Demo
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Free setup • ✓ 30-day trial • ✓ Expert training • ✓ 24/7 support
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(FreshServiceCRM, {
  loadingText: "Loading FreshService CRM...",
  minLoadingTime: 1000
});
