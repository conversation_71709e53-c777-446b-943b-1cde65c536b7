import { supabase, handleSupabaseError } from '@/lib/supabase';
import {
  BlogPost,
  BlogCategory,
  BlogPostPreview,
  BlogFilters,
  BlogSortOption,
  BlogPostsResponse,
  BlogCategoriesResponse,
  BlogError,
} from '@/types/blog';

// Helper function to map database row to BlogPost
const mapDatabaseRowToBlogPost = (row: any): BlogPost => {
  return {
    id: row.id,
    title: row.title,
    excerpt: row.excerpt || '',
    content: row.content,
    author: row.users?.name || row.users?.email || 'Unknown Author',
    authorAvatar: row.users?.avatar_url,
    publishedDate: row.published_at || row.created_at,
    readTime: calculateReadTime(row.content),
    category: row.categories?.name || 'Uncategorized',
    imageUrl: row.featured_image || 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=400&fit=crop',
    slug: row.slug,
    tags: row.post_tags?.map((pt: any) => pt.tags?.name).filter(Boolean) || [],
    isPublished: row.status === 'published',
    metaTitle: row.title,
    metaDescription: row.excerpt,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
};

// Helper function to calculate read time
const calculateReadTime = (content: string): string => {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return `${minutes} min read`;
};

export class BlogService {
  /**
   * Fetch all published blog posts with optional filtering and sorting
   */
  static async getBlogPosts(
    filters: BlogFilters = {},
    sortBy: BlogSortOption = 'newest'
  ): Promise<BlogPostsResponse> {
    if (!supabase) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      let query = supabase
        .from('posts')
        .select(`
          *,
          users:author_id (name, email, avatar_url),
          categories:category_id (name, slug),
          post_tags (
            tags (name, slug)
          )
        `)
        .eq('status', 'published');

      // Apply filters
      if (filters.category) {
        query = query.eq('categories.name', filters.category);
      }

      if (filters.author) {
        query = query.or(`users.name.ilike.%${filters.author}%,users.email.ilike.%${filters.author}%`);
      }

      if (filters.search) {
        query = query.or(
          `title.ilike.%${filters.search}%,excerpt.ilike.%${filters.search}%,content.ilike.%${filters.search}%`
        );
      }

      // Apply sorting
      switch (sortBy) {
        case 'newest':
          query = query.order('published_at', { ascending: false });
          break;
        case 'oldest':
          query = query.order('published_at', { ascending: true });
          break;
        case 'alphabetical':
          query = query.order('title', { ascending: true });
          break;
        case 'popular':
          // For now, sort by newest. In the future, you could add a view_count column
          query = query.order('published_at', { ascending: false });
          break;
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      const posts = data?.map(mapDatabaseRowToBlogPost) || [];
      const total = count || posts.length;
      const hasMore = filters.limit ? posts.length === filters.limit : false;

      return {
        posts,
        total,
        hasMore,
      };
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch blog posts');
    }
  }

  /**
   * Fetch a single blog post by slug
   */
  static async getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    if (!supabase) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users:author_id (name, email, avatar_url),
          categories:category_id (name, slug),
          post_tags (
            tags (name, slug)
          )
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw new Error(handleSupabaseError(error));
      }

      return data ? mapDatabaseRowToBlogPost(data) : null;
    } catch (error) {
      console.error('Error fetching blog post by slug:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch blog post');
    }
  }

  /**
   * Fetch featured blog posts (latest 3 posts)
   */
  static async getFeaturedBlogPosts(limit: number = 3): Promise<BlogPost[]> {
    if (!supabase) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users:author_id (name, email, avatar_url),
          categories:category_id (name, slug),
          post_tags (
            tags (name, slug)
          )
        `)
        .eq('status', 'published')
        .order('published_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data?.map(mapDatabaseRowToBlogPost) || [];
    } catch (error) {
      console.error('Error fetching featured blog posts:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch featured posts');
    }
  }

  /**
   * Fetch all blog categories
   */
  static async getBlogCategories(): Promise<BlogCategory[]> {
    if (!supabase) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data?.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        createdAt: cat.created_at,
      })) || [];
    } catch (error) {
      console.error('Error fetching blog categories:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch categories');
    }
  }

  /**
   * Get unique categories from published blog posts
   */
  static async getPostCategories(): Promise<string[]> {
    if (!supabase) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          categories:category_id (name)
        `)
        .eq('status', 'published')
        .not('category_id', 'is', null);

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      const categories = [...new Set(data?.map(post => post.categories?.name).filter(Boolean) || [])];
      return categories.sort();
    } catch (error) {
      console.error('Error fetching post categories:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch categories');
    }
  }

  /**
   * Search blog posts by title, excerpt, or content
   */
  static async searchBlogPosts(
    searchTerm: string,
    limit: number = 10
  ): Promise<BlogPost[]> {
    if (!supabase) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users:author_id (name, email, avatar_url),
          categories:category_id (name, slug),
          post_tags (
            tags (name, slug)
          )
        `)
        .eq('status', 'published')
        .or(
          `title.ilike.%${searchTerm}%,excerpt.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`
        )
        .order('published_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data?.map(mapDatabaseRowToBlogPost) || [];
    } catch (error) {
      console.error('Error searching blog posts:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to search blog posts');
    }
  }

  /**
   * Get related blog posts based on category and tags
   */
  static async getRelatedPosts(
    currentPostId: string,
    categoryName: string,
    tags: string[] = [],
    limit: number = 3
  ): Promise<BlogPost[]> {
    try {
      let query = supabase
        .from('posts')
        .select(`
          *,
          users:author_id (name, email, avatar_url),
          categories:category_id (name, slug),
          post_tags (
            tags (name, slug)
          )
        `)
        .eq('status', 'published')
        .neq('id', currentPostId);

      // Find posts in the same category
      if (categoryName) {
        query = query.eq('categories.name', categoryName);
      }

      const { data, error } = await query
        .order('published_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data?.map(mapDatabaseRowToBlogPost) || [];
    } catch (error) {
      console.error('Error fetching related posts:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch related posts');
    }
  }
}
