# Deployment Guide

This guide covers how to fix the deployment errors and deploy the application successfully.

## 🚨 Deployment Errors Fixed

### 1. X-Frame-Options Error
**Error**: `X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.`

**Fix**: Removed the X-Frame-Options meta tag from the client-side security configuration and moved it to server-side headers in `vercel.json`.

### 2. Missing Supabase Environment Variables
**Error**: `Missing Supabase environment variables. Please check your .env.local file.`

**Fix**: Updated the Supabase configuration to gracefully handle missing environment variables. The blog functionality will be disabled if Supa<PERSON> is not configured, but the rest of the application will work normally.

### 3. Manifest.json Issues
**Error**: `Manifest: property 'url' ignored, should be within scope of the manifest.`

**Fix**: Updated the manifest.json shortcuts to use relative URLs within the application scope instead of external URLs.

## 🔧 Required Files for Deployment

### 1. Environment Variables
Create a `.env.local` file (or set environment variables in your deployment platform):

```env
# Required for basic functionality
VITE_BASE_URL=https://your-domain.com
VITE_CONTACT_EMAIL=<EMAIL>
VITE_SUPPORT_EMAIL=<EMAIL>

# Optional - for blog functionality (if not provided, blog will be disabled)
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional - for analytics and error reporting
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
```

### 2. Vercel Configuration (vercel.json)
Create a `vercel.json` file in the root directory:

```json
{
  "version": 2,
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
        },
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://cal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://formspree.io https://cal.com https://*.supabase.co; frame-src https://www.google.com https://cal.com; object-src 'none'; base-uri 'self'"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

## 🚀 Deployment Steps

### For Vercel:
1. Connect your GitHub repository to Vercel
2. Set the environment variables in the Vercel dashboard
3. Deploy - the `vercel.json` configuration will be automatically applied

### For Netlify:
1. Create a `_headers` file in the `public` directory:
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://cal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://formspree.io https://cal.com https://*.supabase.co; frame-src https://www.google.com https://cal.com; object-src 'none'; base-uri 'self'
```

2. Create a `_redirects` file in the `public` directory:
```
/*    /index.html   200
```

3. Set environment variables in Netlify dashboard
4. Deploy

### For Other Platforms:
- Ensure the build command is `npm run build`
- Set the output directory to `dist`
- Configure the security headers as shown above
- Set up SPA routing to serve `index.html` for all routes

## 🔍 Testing Deployment

After deployment, test these features:
1. **Basic Navigation**: All pages should load correctly
2. **Blog Functionality**: 
   - If Supabase is configured: Blog posts should load
   - If Supabase is not configured: Blog should show fallback content
3. **Security Headers**: Use tools like securityheaders.com to verify headers
4. **PWA Features**: Check that the manifest.json is valid

## 🛠️ Troubleshooting

### Blog Not Working
- Check if Supabase environment variables are set correctly
- Verify Supabase URL and key are valid
- Check browser console for CSP errors

### Security Header Issues
- Verify the headers are being set by your hosting platform
- Check browser developer tools Network tab to see response headers

### Routing Issues
- Ensure SPA routing is configured (all routes serve index.html)
- Check that the rewrite rules are working

## 📝 Notes

- The application is designed to work without Supabase (blog functionality will be disabled)
- All security headers are configured for production use
- The CSP includes Supabase domains for blog functionality
- PWA features are fully configured and ready for production
