/**
 * Dynamic Sitemap Generation Utilities
 * Generates XML sitemaps with blog posts and other dynamic content
 */

import { BlogPost } from '@/types/blog';
import { BlogService } from '@/services/blogService';

export interface SitemapUrl {
  loc: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  images?: SitemapImage[];
  news?: SitemapNews;
}

export interface SitemapImage {
  loc: string;
  title?: string;
  caption?: string;
}

export interface SitemapNews {
  publication: {
    name: string;
    language: string;
  };
  publication_date: string;
  title: string;
}

/**
 * Static pages configuration for sitemap
 */
export const staticPages: SitemapUrl[] = [
  {
    loc: 'https://eriasoftware.com/',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'daily',
    priority: 1.0,
    images: [{
      loc: 'https://eriasoftware.com/og-image.png',
      title: 'ZYKA POS - Complete Restaurant Management System',
      caption: 'Transform your restaurant operations with ZYKA POS'
    }]
  },
  {
    loc: 'https://eriasoftware.com/point-of-sale',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.95
  },
  {
    loc: 'https://eriasoftware.com/inventory',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.95
  },
  {
    loc: 'https://eriasoftware.com/demo',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.95
  },
  {
    loc: 'https://eriasoftware.com/website-development',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/app-development',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/freshservice-crm',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/contact',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.85
  },
  {
    loc: 'https://eriasoftware.com/support',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.80
  },
  {
    loc: 'https://eriasoftware.com/about-us',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: 0.75
  },
  {
    loc: 'https://eriasoftware.com/blog',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'daily',
    priority: 0.80
  },
  // Legal pages
  {
    loc: 'https://eriasoftware.com/privacy-policy',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.40
  },
  {
    loc: 'https://eriasoftware.com/terms-of-service',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.40
  },
  {
    loc: 'https://eriasoftware.com/cookie-policy',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.35
  },
  {
    loc: 'https://eriasoftware.com/accessibility-statement',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'yearly',
    priority: 0.35
  },
  {
    loc: 'https://eriasoftware.com/security-policy',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'quarterly',
    priority: 0.40
  }
];

/**
 * Convert blog post to sitemap URL
 */
export const blogPostToSitemapUrl = (post: BlogPost): SitemapUrl => {
  const baseUrl = import.meta.env.VITE_BASE_URL || 'https://eriasoftware.com';
  const publishedDate = new Date(post.publishedDate);
  const isRecent = (Date.now() - publishedDate.getTime()) < (30 * 24 * 60 * 60 * 1000); // 30 days

  return {
    loc: `${baseUrl}/blog/${post.slug}`,
    lastmod: publishedDate.toISOString().split('T')[0],
    changefreq: isRecent ? 'weekly' : 'monthly',
    priority: isRecent ? 0.75 : 0.70,
    images: post.imageUrl ? [{
      loc: post.imageUrl,
      title: post.title,
      caption: post.excerpt
    }] : undefined,
    news: isRecent ? {
      publication: {
        name: 'ERIA Software Blog',
        language: 'en'
      },
      publication_date: publishedDate.toISOString().split('T')[0],
      title: post.title
    } : undefined
  };
};

/**
 * Fetch all blog posts for sitemap
 */
export const getBlogPostsForSitemap = async (): Promise<SitemapUrl[]> => {
  try {
    // Check if Supabase is configured
    const isSupabaseConfigured = !!(
      import.meta.env.VITE_SUPABASE_URL &&
      import.meta.env.VITE_SUPABASE_ANON_KEY
    );

    if (!isSupabaseConfigured) {
      console.log('Supabase not configured, returning empty blog posts array for sitemap');
      return [];
    }

    const result = await BlogService.getBlogPosts({ limit: 1000 }, 'newest');
    return result.posts.map(blogPostToSitemapUrl);
  } catch (error) {
    console.error('Error fetching blog posts for sitemap:', error);
    return [];
  }
};

/**
 * Generate complete sitemap URLs
 */
export const generateSitemapUrls = async (): Promise<SitemapUrl[]> => {
  const blogUrls = await getBlogPostsForSitemap();
  return [...staticPages, ...blogUrls];
};

/**
 * Generate XML sitemap string
 */
export const generateSitemapXML = async (): Promise<string> => {
  const urls = await generateSitemapUrls();
  
  const urlsXML = urls.map(url => {
    let urlXML = `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>`;

    // Add images if present
    if (url.images && url.images.length > 0) {
      url.images.forEach(image => {
        urlXML += `
    <image:image>
      <image:loc>${image.loc}</image:loc>`;
        if (image.title) urlXML += `
      <image:title>${escapeXML(image.title)}</image:title>`;
        if (image.caption) urlXML += `
      <image:caption>${escapeXML(image.caption)}</image:caption>`;
        urlXML += `
    </image:image>`;
      });
    }

    // Add news if present
    if (url.news) {
      urlXML += `
    <news:news>
      <news:publication>
        <news:name>${escapeXML(url.news.publication.name)}</news:name>
        <news:language>${url.news.publication.language}</news:language>
      </news:publication>
      <news:publication_date>${url.news.publication_date}</news:publication_date>
      <news:title>${escapeXML(url.news.title)}</news:title>
    </news:news>`;
    }

    urlXML += `
  </url>`;
    return urlXML;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
${urlsXML}
</urlset>`;
};

/**
 * Escape XML special characters
 */
const escapeXML = (str: string): string => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
};

/**
 * Save sitemap to public directory (for build process)
 */
export const saveSitemapToFile = async (): Promise<void> => {
  try {
    const sitemapXML = await generateSitemapXML();
    
    // In a real application, you would write this to the public directory
    // For now, we'll log it or handle it based on the environment
    console.log('Generated sitemap XML:', sitemapXML);
    
    // This would typically be handled by a build script or server-side function
    // that writes the sitemap to public/sitemap.xml
  } catch (error) {
    console.error('Error generating sitemap:', error);
  }
};
