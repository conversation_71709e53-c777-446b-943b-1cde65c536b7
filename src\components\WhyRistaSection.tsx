import { 
  Clock, 
  ShoppingBag, 
  Recycle, 
  TrendingUp, 
  Heart, 
  Zap,
  Headphones,
  Shield,
  ArrowUpRight,
  Smartphone,
  RefreshCcw,
  Globe
} from "lucide-react";

const WhyRistaSection = () => {
  const benefits = [
    {
      icon: Clock,
      title: "Lightning-Fast Service",
      description: "Reduce service time by 40% with advanced order processing and integrated kitchen displays."
    },
    {
      icon: ShoppingBag,
      title: "Smart Inventory Control",
      description: "Automated alerts and predictive reordering prevent stockouts while reducing costs by 25%."
    },
    {
      icon: Recycle,
      title: "Waste Reduction",
      description: "Precision tracking and analytics reduce food waste by 25% while maintaining quality."
    },
    {
      icon: TrendingUp,
      title: "Revenue Growth",
      description: "AI-powered upselling and WhatsApp marketing increase revenue by 20-30%."
    },
    {
      icon: Heart,
      title: "Customer Loyalty",
      description: "Personalized engagement tools increase repeat visits by 35% and boost lifetime value."
    },
    {
      icon: Zap,
      title: "Easy Integrations",
      description: "Connect with 50+ business tools through our robust API for seamless operations."
    },
    {
      icon: Clock,
      title: "Quick Setup",
      description: "Get operational within 24 hours with intuitive interface and comprehensive training."
    },
    {
      icon: Headphones,
      title: "24/7 Support",
      description: "Expert support with 2-minute response times and 99.9% issue resolution rate."
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Military-grade encryption and PCI DSS compliance protect your sensitive data."
    },
    {
      icon: ArrowUpRight,
      title: "Unlimited Scalability",
      description: "Cloud-based architecture grows from single location to multi-chain operations."
    },
    {
      icon: RefreshCcw,
      title: "Regular Updates",
      description: "Monthly feature releases keep you ahead with cutting-edge capabilities."
    },
    {
      icon: Smartphone,
      title: "Mobile Management",
      description: "Control operations anywhere with comprehensive mobile app functionality."
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-muted/20">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-sm font-semibold text-primary mb-4 uppercase tracking-wide">
            WHY Zyka POS?
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4">
            The Complete Restaurant Management Solution That Drives Results
          </h2>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index} 
              className="bg-background rounded-lg p-6 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <div className="space-y-4">
                <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <benefit.icon className="w-6 h-6 text-primary-foreground" />
                </div>
                
                <h3 className="text-lg font-semibold text-foreground">
                  {benefit.title}
                </h3>
                
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyRistaSection;