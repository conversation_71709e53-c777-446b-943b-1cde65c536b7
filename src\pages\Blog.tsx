import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BlogFallback from "@/components/BlogFallback";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import withPageLoader from "@/components/withPageLoader";
import { Calendar, User, ArrowRight, Clock, AlertCircle, Loader2 } from "lucide-react";
import { BlogService } from "@/services/blogService";
import { formatBlogDate } from "@/types/blog";

const Blog = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string>("All Posts");
  const [searchTerm, setSearchTerm] = useState<string>("");

  // Check if Supabase is configured
  const isSupabaseConfigured = !!(
    import.meta.env.VITE_SUPABASE_URL &&
    import.meta.env.VITE_SUPABASE_ANON_KEY
  );



  // Fetch blog posts
  const {
    data: blogPostsData,
    isLoading: postsLoading,
    error: postsError,
    refetch: refetchPosts,
  } = useQuery({
    queryKey: ['blogPosts', selectedCategory, searchTerm],
    queryFn: async () => {
      const filters = {
        category: selectedCategory === "All Posts" ? undefined : selectedCategory,
        search: searchTerm || undefined,
        limit: 50, // Fetch more posts for better UX
      };
      return await BlogService.getBlogPosts(filters, 'newest');
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    enabled: isSupabaseConfigured, // Only run query if Supabase is configured
  });

  // Fetch categories
  const {
    data: categories,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useQuery({
    queryKey: ['blogCategories'],
    queryFn: async () => {
      const postCategories = await BlogService.getPostCategories();
      return ["All Posts", ...postCategories];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    enabled: isSupabaseConfigured, // Only run query if Supabase is configured
  });

  const blogPosts = blogPostsData?.posts || [];
  const featuredPost = blogPosts[0];
  const otherPosts = blogPosts.slice(1);

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setSearchTerm(""); // Clear search when changing category
  };

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setSelectedCategory("All Posts"); // Reset category when searching
  };

  // If Supabase is not configured, show fallback
  if (!isSupabaseConfigured) {
    return (
      <div className="min-h-screen bg-background">
        <Header />

        {/* Hero Section */}
        <section className="w-full bg-gradient-hero py-16 lg:py-24">
          <div className="container mx-auto px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto space-y-8">
              <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                Knowledge Hub
              </Badge>

              <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                Eria Software Blog
              </h1>

              <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                Stay updated with the latest insights, tips, and trends in restaurant technology,
                business operations, and industry best practices.
              </p>
            </div>
          </div>
        </section>

        {/* Fallback Content */}
        <section className="w-full py-16 lg:py-24 bg-background">
          <div className="container mx-auto px-6 lg:px-8">
            <BlogFallback
              error="Supabase environment variables not found"
              showSetupGuide={true}
            />
          </div>
        </section>

        <Footer />
      </div>
    );
  }

  // Loading skeleton component
  const PostSkeleton = () => (
    <Card className="border-0 shadow-soft bg-background overflow-hidden">
      <Skeleton className="w-full h-48" />
      <CardContent className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
            <Skeleton className="h-3 w-12" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Error component
  const ErrorAlert = ({ message, onRetry }: { message: string; onRetry: () => void }) => (
    <Alert variant="destructive" className="max-w-2xl mx-auto">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span>{message}</span>
        <Button variant="outline" size="sm" onClick={onRetry}>
          Try Again
        </Button>
      </AlertDescription>
    </Alert>
  );

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              Knowledge Hub
            </Badge>

            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Eria Software Blog
            </h1>

            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Stay updated with the latest insights, tips, and trends in restaurant technology,
              business operations, and industry best practices.
            </p>

            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="w-full py-8 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          {categoriesLoading ? (
            <div className="flex justify-center">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : categoriesError ? (
            <div className="text-center text-red-600">
              Failed to load categories
            </div>
          ) : (
            <div className="flex flex-wrap justify-center gap-4">
              {categories?.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleCategorySelect(category)}
                  className={selectedCategory === category ? "bg-blue-600 text-white hover:bg-blue-700" : ""}
                >
                  {category}
                </Button>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Error State */}
      {postsError && (
        <section className="w-full py-16 bg-background">
          <div className="container mx-auto px-6 lg:px-8">
            <ErrorAlert
              message="Failed to load blog posts. Please try again."
              onRetry={() => refetchPosts()}
            />
          </div>
        </section>
      )}

      {/* Featured Post */}
      {!postsError && (
        <section className="w-full py-16 lg:py-24 bg-background">
          <div className="container mx-auto px-6 lg:px-8">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
                  {searchTerm ? "Search Results" : "Featured Article"}
                </h2>
                {searchTerm && (
                  <p className="text-muted-foreground">
                    Found {blogPosts.length} article{blogPosts.length !== 1 ? 's' : ''} for "{searchTerm}"
                  </p>
                )}
              </div>

              {postsLoading ? (
                <Card className="border-0 shadow-soft bg-background overflow-hidden">
                  <div className="grid lg:grid-cols-2 gap-0">
                    <Skeleton className="w-full h-64 lg:h-96" />
                    <CardContent className="p-8 lg:p-12 flex flex-col justify-center">
                      <div className="space-y-4">
                        <Skeleton className="h-8 w-3/4" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-5/6" />
                        <div className="flex items-center gap-4">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                        <Skeleton className="h-6 w-32" />
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ) : featuredPost ? (
                <Card
                  className="border-0 shadow-soft bg-background overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300"
                  onClick={() => navigate(`/blog/${featuredPost.slug}`)}
                >
                  <div className="grid lg:grid-cols-2 gap-0">
                    <div className="relative">
                      <img
                        src={featuredPost.imageUrl}
                        alt={featuredPost.title}
                        className="w-full h-64 lg:h-full object-cover transition-transform duration-300 hover:scale-105"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop";
                        }}
                      />
                      <Badge className="absolute top-4 left-4 bg-blue-600 text-white">
                        {featuredPost.category}
                      </Badge>
                    </div>
                    <CardContent className="p-8 lg:p-12 flex flex-col justify-center">
                      <div className="space-y-4">
                        <h3 className="text-2xl lg:text-3xl font-bold text-foreground leading-tight hover:text-blue-600 transition-colors">
                          {featuredPost.title}
                        </h3>

                        <p className="text-muted-foreground leading-relaxed">
                          {featuredPost.excerpt}
                        </p>

                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            <span>{featuredPost.author}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            <span>{formatBlogDate(featuredPost.publishedDate)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{featuredPost.readTime}</span>
                          </div>
                        </div>

                        <Button variant="link" className="p-0 font-semibold text-blue-600 hover:text-blue-700">
                          Read Full Article
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground text-lg">
                    {searchTerm ? "No articles found matching your search." : "No blog posts available at the moment."}
                  </p>
                  {searchTerm && (
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedCategory("All Posts");
                      }}
                    >
                      Clear Search
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Blog Posts Grid */}
      {!postsError && otherPosts.length > 0 && (
        <section className="w-full py-16 lg:py-24 bg-muted/30">
          <div className="container mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
                {searchTerm ? "More Results" : "Latest Articles"}
              </h2>
              {!searchTerm && (
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Explore our collection of articles covering restaurant technology, business tips, and industry insights.
                </p>
              )}
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {postsLoading ? (
                // Loading skeletons
                Array.from({ length: 6 }).map((_, index) => (
                  <PostSkeleton key={index} />
                ))
              ) : (
                otherPosts.map((post) => (
                  <Card
                    key={post.id}
                    className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background overflow-hidden cursor-pointer"
                    onClick={() => navigate(`/blog/${post.slug}`)}
                  >
                    <div className="relative">
                      <img
                        src={post.imageUrl}
                        alt={post.title}
                        className="w-full h-48 object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop";
                        }}
                      />
                      <Badge className="absolute top-4 left-4 bg-blue-600 text-white text-xs">
                        {post.category}
                      </Badge>
                      {post.tags && post.tags.length > 0 && (
                        <div className="absolute top-4 right-4">
                          <Badge variant="secondary" className="text-xs bg-white/90 text-gray-700">
                            {post.tags[0]}
                          </Badge>
                        </div>
                      )}
                    </div>

                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-foreground leading-tight line-clamp-2">
                          {post.title}
                        </h3>

                        <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
                          {post.excerpt}
                        </p>

                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center gap-3">
                            <span className="font-medium">{post.author}</span>
                            <span>{formatBlogDate(post.publishedDate)}</span>
                          </div>
                          <span className="font-medium">{post.readTime}</span>
                        </div>

                        <Button variant="link" className="p-0 font-semibold text-blue-600 hover:text-blue-700 text-sm">
                          Read More
                          <ArrowRight className="w-3 h-3 ml-1" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>

            {/* Load More Button (for future pagination) */}
            {!postsLoading && !searchTerm && blogPostsData?.hasMore && (
              <div className="text-center mt-12">
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8"
                  onClick={() => {
                    // Implement load more functionality in the future
                    console.log('Load more posts');
                  }}
                >
                  Load More Articles
                </Button>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Newsletter Signup */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Stay Updated with Our Latest Insights
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Subscribe to our newsletter and get the latest articles, tips, and industry news delivered to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Enter your email address"
                className="w-full px-4 py-3 rounded-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Button
                variant="hero"
                size="lg"
                className="w-full sm:w-auto text-lg px-8 py-3 bg-blue-600 text-white hover:bg-blue-700"
              >
                Subscribe
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Weekly insights • ✓ Industry trends • ✓ Expert tips • ✓ No spam, unsubscribe anytime
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(Blog, {
  loadingText: "Loading Blog...",
  minLoadingTime: 900
});
