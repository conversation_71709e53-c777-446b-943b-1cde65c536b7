import { Card, CardContent } from "@/components/ui/card";

const RestaurantTypesSection = () => {
  const restaurantTypes = [
    {
      title: "Fine Dining & Premium Bars",
      features: [
        "Advanced table reservation system with guest preferences",
        "Sophisticated waiter app with course timing controls",
        "Wine pairing suggestions and premium inventory tracking",
        "Detailed guest history and personalized service notes"
      ],
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop"
    },
    {
      title: "Quick Service Restaurants",
      features: [
        "Lightning-fast order processing under 30 seconds",
        "Dynamic menu scheduling for peak hours",
        "Automated inventory depletion with real-time alerts",
        "Self-service kiosk integration for reduced wait times"
      ],
      image: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=600&h=400&fit=crop"
    },
    {
      title: "Cloud Kitchens & Virtual Brands",
      features: [
        "Multi-brand order management from single dashboard",
        "Seamless integration with Zomato, Swiggy, and UberEats",
        "Unified inventory tracking across multiple virtual brands",
        "Commission tracking and profitability analysis per platform"
      ],
      image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop"
    },
    {
      title: "Pubs, Bars, Clubs & Lounges",
      features: [
        "Multi-terminal billing for high-volume environments",
        "Advanced liquor inventory with pour cost tracking",
        "Flexible bill splitting for groups and events",
        "Happy hour pricing and promotional management"
      ],
      image: "https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=600&h=400&fit=crop"
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-muted/20">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-sm font-semibold text-primary mb-4 uppercase tracking-wide">
            RESTAURANT TYPES
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4">
            Tailored Solutions for Every Restaurant Format
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            From fine dining establishments to quick-service restaurants, Zyka POS adapts to your
            specific business model with industry-focused features that maximize efficiency and profitability
            across all restaurant formats.
          </p>
        </div>

        {/* Restaurant Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {restaurantTypes.map((type, index) => (
            <Card
              key={index}
              className="overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 md:hover:scale-105 bg-background border-0"
            >
              <CardContent className="p-0">
                <div className="relative">
                  <img
                    src={type.image}
                    alt={type.title}
                    className="w-full h-48 sm:h-56 md:h-64 object-cover"
                  />
                  <div className="absolute inset-0 bg-black/50"></div>

                  <div className="absolute bottom-4 left-4 right-4 sm:bottom-6 sm:left-6 sm:right-6 text-white">
                    <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-3 md:mb-4 leading-tight">
                      {type.title}
                    </h3>

                    <ul className="space-y-1.5 sm:space-y-2">
                      {type.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></div>
                          <span className="text-xs sm:text-sm leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default RestaurantTypesSection;