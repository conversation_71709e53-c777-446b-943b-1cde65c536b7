import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  Globe, 
  Smartphone, 
  Zap, 
  Search, 
  ShoppingCart, 
  Shield,
  Palette,
  Code,
  TrendingUp,
  CheckCircle
} from "lucide-react";

const WebsiteDevelopment = () => {
  const features = [
    {
      icon: Globe,
      title: "Custom Restaurant Websites",
      description: "Beautiful, responsive websites tailored specifically for restaurants and food businesses."
    },
    {
      icon: Smartphone,
      title: "Mobile-First Design",
      description: "Optimized for mobile devices to capture customers on-the-go."
    },
    {
      icon: ShoppingCart,
      title: "Online Ordering Integration",
      description: "Seamless integration with your POS system for direct online orders."
    },
    {
      icon: Search,
      title: "SEO Optimized",
      description: "Built for search engines to help customers find your restaurant easily."
    },
    {
      icon: Zap,
      title: "Fast Loading",
      description: "Lightning-fast websites that load in under 3 seconds for better user experience."
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      description: "SSL certificates, security monitoring, and 99.9% uptime guarantee."
    }
  ];

  const services = [
    "Custom restaurant website design",
    "Online menu and ordering system",
    "Table reservation system",
    "SEO optimization and marketing",
    "Social media integration",
    "Analytics and performance tracking"
  ];

  const portfolioItems = [
    {
      title: "Fine Dining Restaurant",
      description: "Elegant website with reservation system",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop"
    },
    {
      title: "Fast Food Chain",
      description: "Quick ordering system with delivery",
      image: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=300&fit=crop"
    },
    {
      title: "Cafe & Bakery",
      description: "Cozy design with online menu",
      image: "https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=400&h=300&fit=crop"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                  Professional Website Development
                </Badge>
                
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  Custom Restaurant Websites That Drive Orders
                </h1>
                
                <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                  Get a stunning, mobile-optimized website with integrated online ordering 
                  that converts visitors into customers and grows your restaurant business.
                </p>
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Get Free Quote
              </Button>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">Free consultation & quote</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">30-day money-back guarantee</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop" 
                alt="Website Development" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Complete Website Solutions for Restaurants
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to establish a strong online presence and attract more customers.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                Our Website Development Services
              </h2>
              
              <div className="space-y-4">
                {services.map((service, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-foreground">{service}</span>
                  </div>
                ))}
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Start Your Project
              </Button>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop" 
                alt="Web Development Services" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Our Recent Work
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              See how we've helped restaurants create stunning websites that drive business growth.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {portfolioItems.map((item, index) => (
              <Card key={index} className="overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background border-0">
                <CardContent className="p-0">
                  <img 
                    src={item.image} 
                    alt={item.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-foreground mb-2">
                      {item.title}
                    </h3>
                    <p className="text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Launch Your Restaurant Website?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Get a professional website that attracts customers and drives online orders.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              >
                Get Free Quote
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                View Portfolio
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Free consultation • ✓ Custom design • ✓ SEO optimized • ✓ Mobile responsive
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(WebsiteDevelopment, {
  loadingText: "Loading Website Development...",
  minLoadingTime: 1000
});
