/**
 * Performance Optimizer Component
 * Implements various performance optimizations for better Core Web Vitals
 */

import { useEffect } from 'react';

interface PerformanceOptimizerProps {
  preloadImages?: string[];
  preloadFonts?: string[];
  enableServiceWorker?: boolean;
}

const PerformanceOptimizer = ({ 
  preloadImages = [], 
  preloadFonts = [],
  enableServiceWorker = false 
}: PerformanceOptimizerProps) => {
  
  useEffect(() => {
    // Preload critical images
    preloadImages.forEach(imageUrl => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = imageUrl;
      link.setAttribute('data-component', 'performance-optimizer');
      document.head.appendChild(link);
    });

    // Preload critical fonts
    preloadFonts.forEach(fontUrl => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.href = fontUrl;
      link.crossOrigin = 'anonymous';
      link.setAttribute('data-component', 'performance-optimizer');
      document.head.appendChild(link);
    });

    // Add resource hints for better performance
    addResourceHints();

    // Optimize images with lazy loading
    optimizeImages();

    // Add performance monitoring
    addPerformanceMonitoring();

    // Register service worker if enabled
    if (enableServiceWorker) {
      registerServiceWorker();
    }

    return () => {
      // Cleanup preload links on unmount
      const preloadLinks = document.querySelectorAll('link[data-component="performance-optimizer"]');
      preloadLinks.forEach(link => link.remove());
    };
  }, [preloadImages, preloadFonts, enableServiceWorker]);

  return null; // This component doesn't render anything visible
};

const addResourceHints = () => {
  const hints = [
    // DNS prefetch for external resources
    { rel: 'dns-prefetch', href: 'https://fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: 'https://fonts.gstatic.com' },
    { rel: 'dns-prefetch', href: 'https://images.unsplash.com' },
    { rel: 'dns-prefetch', href: 'https://www.google-analytics.com' },
    { rel: 'dns-prefetch', href: 'https://www.googletagmanager.com' },
    
    // Preconnect for critical resources
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' },
  ];

  hints.forEach(hint => {
    // Check if hint already exists
    const selector = hint.crossOrigin 
      ? `link[rel="${hint.rel}"][href="${hint.href}"][crossorigin]`
      : `link[rel="${hint.rel}"][href="${hint.href}"]`;
    
    if (!document.querySelector(selector)) {
      const link = document.createElement('link');
      link.rel = hint.rel;
      link.href = hint.href;
      if (hint.crossOrigin) {
        link.crossOrigin = hint.crossOrigin;
      }
      link.setAttribute('data-component', 'performance-optimizer');
      document.head.appendChild(link);
    }
  });
};

const optimizeImages = () => {
  // Add intersection observer for lazy loading images
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            observer.unobserve(img);
          }
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    });

    // Observe all images with data-src attribute
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => imageObserver.observe(img));
  }

  // Add loading="lazy" to images that don't have it
  const images = document.querySelectorAll('img:not([loading])');
  images.forEach((img, index) => {
    // Don't lazy load the first few images (above the fold)
    if (index > 2) {
      img.setAttribute('loading', 'lazy');
    }
  });
};

const addPerformanceMonitoring = () => {
  // Monitor Core Web Vitals
  if ('web-vitals' in window || typeof window !== 'undefined') {
    // Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      // Log LCP for monitoring (in production, send to analytics)
      console.log('LCP:', lastEntry.startTime);
      
      // You can send this data to your analytics service
      // analytics.track('Core Web Vital', { metric: 'LCP', value: lastEntry.startTime });
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      // Fallback for browsers that don't support LCP
      console.log('LCP monitoring not supported');
    }

    // First Input Delay (FID) - measure when user first interacts
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        console.log('FID:', entry.processingStart - entry.startTime);
        // analytics.track('Core Web Vital', { metric: 'FID', value: entry.processingStart - entry.startTime });
      });
    });

    try {
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e) {
      console.log('FID monitoring not supported');
    }

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      console.log('CLS:', clsValue);
      // analytics.track('Core Web Vital', { metric: 'CLS', value: clsValue });
    });

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      console.log('CLS monitoring not supported');
    }
  }
};

const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered successfully:', registration);
    } catch (error) {
      console.log('Service Worker registration failed:', error);
    }
  }
};

// Default preload resources for the homepage
export const defaultPreloadResources = {
  images: [
    '/og-image.png',
    '/logo.png',
    '/hero-bg.jpg', // Add your hero background image
  ],
  fonts: [
    'https://fonts.gstatic.com/s/manrope/v15/xn7_YHE41ni1AdIRqAuZuw1Bx9mbZk59FO_F87jxeN7B.woff2',
  ]
};

export default PerformanceOptimizer;
