import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  Users, 
  Target, 
  Award, 
  Heart, 
  Lightbulb, 
  Shield,
  TrendingUp,
  Globe,
  CheckCircle,
  Star
} from "lucide-react";

const AboutUs = () => {
  const values = [
    {
      icon: Heart,
      title: "Customer First",
      description: "We put our customers at the center of everything we do, ensuring their success is our success."
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "Constantly evolving our technology to stay ahead of industry trends and customer needs."
    },
    {
      icon: Shield,
      title: "Reliability",
      description: "Building robust, secure solutions that restaurants can depend on 24/7."
    },
    {
      icon: Users,
      title: "Partnership",
      description: "Working closely with our clients as true partners in their business growth journey."
    }
  ];



  const stats = [
    { number: "100+", label: "Active Restaurants", icon: Globe },
    { number: "10+", label: "Cities Covered", icon: Award },
    { number: "100K+", label: "Orders Processed", icon: TrendingUp },
    { number: "20+", label: "Team Members", icon: Users }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              About Eria Software
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Empowering Restaurants with Technology
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Eria Software Solutions & Services Private Limited is a leading provider of comprehensive
              technology solutions for restaurants, cafes, and businesses across India. From our flagship
              ERIA POS software to custom development services, we build industry-ready solutions that
              transform businesses and drive growth.
            </p>

            <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
              Get in Touch
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl lg:text-4xl font-bold text-foreground mb-2">{stat.number}</div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                  Our Mission
                </h2>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  To empower businesses with innovative technology solutions that drive growth,
                  efficiency, and success. From ERIA POS software to comprehensive development
                  services, we transform businesses across India with industry-ready solutions.
                </p>
              </div>

              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-foreground">Our Vision</h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  To become India's most trusted technology partner for businesses, providing
                  comprehensive solutions from POS systems to custom development that enable
                  sustainable growth and digital transformation.
                </p>
              </div>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=400&fit=crop" 
                alt="Our Mission" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Our Core Values
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do and shape our company culture.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background text-center"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
                      <value.icon className="w-8 h-8 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {value.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {value.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Get in Touch with Us
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Ready to transform your business? Contact our team for personalized solutions and support.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background text-center">
              <CardContent className="p-8">
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                    <Users className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-xl font-semibold text-foreground">
                    Contact Support
                  </h3>

                  <div className="space-y-2">
                    <p className="text-muted-foreground">
                      <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                    <p className="text-muted-foreground">
                      <a href="tel:+919604069989" className="hover:text-blue-600 transition-colors">
                        +91 9604069989
                      </a>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-soft bg-background text-center">
              <CardContent className="p-8">
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                    <Globe className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-xl font-semibold text-foreground">
                    General Inquiries
                  </h3>

                  <div className="space-y-2">
                    <p className="text-muted-foreground">
                      <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                    <p className="text-muted-foreground">
                      <a href="tel:+919604069989" className="hover:text-blue-600 transition-colors">
                        +91 9604069989
                      </a>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop" 
                alt="Why Choose Us" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>

            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                Why Restaurants Choose Eria Software
              </h2>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-500 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Industry Expertise</h4>
                    <p className="text-muted-foreground">Deep understanding of restaurant operations and challenges</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-500 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Proven Track Record</h4>
                    <p className="text-muted-foreground">Successfully serving 50+ businesses across India</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-500 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Continuous Innovation</h4>
                    <p className="text-muted-foreground">Regular updates and new features based on industry trends</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-500 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">24/7 Support</h4>
                    <p className="text-muted-foreground">Round-the-clock technical support and customer service</p>
                  </div>
                </div>
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Partner with Us
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Transform Your Restaurant?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Join 50+ successful businesses that trust Eria Software for their technology needs.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              >
                Get Started Today
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                Contact Us
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Free consultation • ✓ Custom solutions • ✓ Proven results • ✓ Ongoing support
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(AboutUs, {
  loadingText: "Loading About Us...",
  minLoadingTime: 900
});
