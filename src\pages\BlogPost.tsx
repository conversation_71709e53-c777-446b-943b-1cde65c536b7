import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import withPageLoader from "@/components/withPageLoader";
import { 
  Calendar, 
  User, 
  ArrowLeft, 
  Clock, 
  AlertCircle, 
  Share2,
  Twitter,
  Facebook,
  Linkedin,
  Copy,
  Check
} from "lucide-react";
import { BlogService } from "@/services/blogService";
import { formatBlogDate } from "@/types/blog";
import { updateBlogPostSEO } from "@/lib/seo";
import { formatBlogContent } from "@/lib/contentUtils";
import { toast } from "@/hooks/use-toast";

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [copied, setCopied] = useState(false);

  // Check if Supabase is configured
  const isSupabaseConfigured = !!(
    import.meta.env.VITE_SUPABASE_URL &&
    import.meta.env.VITE_SUPABASE_ANON_KEY
  );

  // Fetch the blog post
  const {
    data: post,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['blogPost', slug],
    queryFn: async () => {
      if (!slug) throw new Error('No slug provided');
      return await BlogService.getBlogPostBySlug(slug);
    },
    enabled: isSupabaseConfigured && !!slug,
    retry: 2,
  });

  // Fetch related posts
  const {
    data: relatedPostsData,
  } = useQuery({
    queryKey: ['relatedPosts', post?.category],
    queryFn: async () => {
      if (!post?.category) return { posts: [] };
      const filters = {
        category: post.category,
        limit: 3,
      };
      return await BlogService.getBlogPosts(filters, 'newest');
    },
    enabled: isSupabaseConfigured && !!post?.category,
    retry: 1,
  });

  const relatedPosts = relatedPostsData?.posts?.filter(p => p.id !== post?.id) || [];

  // Update SEO when post loads
  useEffect(() => {
    if (post) {
      updateBlogPostSEO({
        title: post.title,
        excerpt: post.excerpt,
        author: post.author,
        publishedDate: post.publishedDate,
        imageUrl: post.imageUrl,
        slug: post.slug,
        tags: post.tags,
        category: post.category,
      });
    }
  }, [post]);

  // Handle sharing
  const handleShare = async (platform: string) => {
    if (!post) return;

    const url = window.location.href;
    const title = post.title;
    const text = post.excerpt;

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          setCopied(true);
          toast({
            title: "Link copied!",
            description: "The blog post link has been copied to your clipboard.",
          });
          setTimeout(() => setCopied(false), 2000);
        } catch (err) {
          toast({
            title: "Failed to copy",
            description: "Please copy the URL manually from your browser.",
            variant: "destructive",
          });
        }
        break;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-6 lg:px-8 py-16">
          <div className="max-w-4xl mx-auto space-y-8">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-12 w-full" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <Skeleton className="h-64 w-full" />
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error || !post) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-6 lg:px-8 py-16">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <AlertCircle className="w-16 h-16 text-muted-foreground mx-auto" />
            <h1 className="text-3xl font-bold text-foreground">Post Not Found</h1>
            <p className="text-muted-foreground">
              The blog post you're looking for doesn't exist or has been removed.
            </p>
            <Button onClick={() => navigate('/blog')} className="mt-6">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-6 lg:px-8 py-8">
        {/* Back to Blog Button */}
        <div className="max-w-4xl mx-auto mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/blog')}
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Blog
          </Button>
        </div>

        {/* Article Header */}
        <article className="max-w-4xl mx-auto">
          <header className="mb-8 space-y-6">
            {/* Category Badge */}
            <Badge variant="secondary" className="text-sm">
              {post.category}
            </Badge>

            {/* Title */}
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground leading-tight">
              {post.title}
            </h1>

            {/* Excerpt */}
            <p className="text-xl text-muted-foreground leading-relaxed">
              {post.excerpt}
            </p>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground border-b border-border pb-6">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span className="font-medium">{post.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatBlogDate(post.publishedDate)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{post.readTime}</span>
              </div>
            </div>

            {/* Share Buttons */}
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-muted-foreground">Share:</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare('twitter')}
                  className="text-blue-500 hover:text-blue-600"
                >
                  <Twitter className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare('facebook')}
                  className="text-blue-600 hover:text-blue-700"
                >
                  <Facebook className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare('linkedin')}
                  className="text-blue-700 hover:text-blue-800"
                >
                  <Linkedin className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare('copy')}
                  className="text-gray-600 hover:text-gray-700"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </header>

          {/* Featured Image */}
          {post.imageUrl && (
            <div className="mb-8">
              <img
                src={post.imageUrl}
                alt={post.title}
                className="w-full h-64 lg:h-96 object-cover rounded-lg shadow-soft"
              />
            </div>
          )}

          {/* Article Content */}
          <div
            className="prose prose-lg max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-a:text-blue-600 hover:prose-a:text-blue-700"
            dangerouslySetInnerHTML={{ __html: formatBlogContent(post.content) }}
          />

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="mt-12 pt-8 border-t border-border">
              <h3 className="text-lg font-semibold text-foreground mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </article>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section className="max-w-4xl mx-auto mt-16 pt-16 border-t border-border">
            <h2 className="text-2xl font-bold text-foreground mb-8">Related Articles</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.slice(0, 3).map((relatedPost) => (
                <Card
                  key={relatedPost.id}
                  className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background overflow-hidden cursor-pointer"
                  onClick={() => navigate(`/blog/${relatedPost.slug}`)}
                >
                  <div className="aspect-video overflow-hidden">
                    <img
                      src={relatedPost.imageUrl}
                      alt={relatedPost.title}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                    />
                  </div>

                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <Badge variant="secondary" className="text-xs">
                        {relatedPost.category}
                      </Badge>

                      <h3 className="text-lg font-semibold text-foreground leading-tight line-clamp-2">
                        {relatedPost.title}
                      </h3>

                      <p className="text-muted-foreground text-sm leading-relaxed line-clamp-2">
                        {relatedPost.excerpt}
                      </p>

                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span className="font-medium">{relatedPost.author}</span>
                        <span>{formatBlogDate(relatedPost.publishedDate)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default withPageLoader(BlogPost, {
  loadingText: "Loading blog post...",
  minLoadingTime: 800
});
